// 路由配置示例
// 在您的路由文件中添加以下配置

{
  path: '/product-picture/wms-image-moderation/:id/:skuIndex?',
  name: 'WmsImageModeration',
  component: () => import('@/views/productPicture/components/wmsImageModeration.vue'),
  meta: {
    title: 'WMS图片审核',
    requiresAuth: true
  }
}

// 使用示例：
// 从列表页面跳转到审核页面
this.$router.push({
  name: 'WmsImageModeration',
  params: {
    id: taskId,        // 任务ID
    skuIndex: 0        // SKU索引，可选，默认为0
  }
});

// 或者使用路径方式
this.$router.push(`/product-picture/wms-image-moderation/${taskId}/0`);
